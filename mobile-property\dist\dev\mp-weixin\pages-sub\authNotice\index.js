"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const content = common_vendor.ref(
      '<h3 style="text-align: center;">授权须知</h3><ol><li>授权人应为本人自愿作出授权决定，并确认被授权人为其直系亲属（包括父母、配偶、子女）。</li><li>授权人授权后，被授权人有权以授权人名义参与相关投票活动，其投票行为视同授权人本人行为，具有同等法律效力。</li><li>授权关系一经确认，授权人及被授权人均应遵守本平台相关规定，诚实守信，不得以任何形式转让、出售或非法利用授权权利。</li><li>授权人有权随时撤销授权，撤销后，被授权人自撤销之时起丧失以授权人名义参与投票的权利。</li><li>授权人与被授权人应保证所提供信息真实、准确、完整，因虚假信息或不当授权产生的法律责任，由相关责任方自行承担。</li><li>本须知未尽事宜，依照国家相关法律法规及本平台管理规定执行。</li></ol><p><br></p>'
    );
    return (_ctx, _cache) => {
      return {
        a: common_vendor.unref(content)
      };
    };
  }
});
wx.createPage(_sfc_main);

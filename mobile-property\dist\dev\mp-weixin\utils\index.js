"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
const utils_platform = require("./platform.js");
const pages = [
  {
    path: "pages/home/<USER>",
    type: "home",
    style: {
      navigationBarTitleText: "首页"
    }
  },
  {
    path: "pages/message/index",
    type: "page",
    style: {
      navigationBarTitleText: "消息"
    }
  },
  {
    path: "pages/mine/index",
    type: "page",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "我的"
    }
  }
];
const subPackages = [
  {
    root: "pages-sub",
    pages: [
      {
        path: "authNotice/index",
        type: "page",
        style: {
          navigationBarTitleText: "授权须知"
        }
      },
      {
        path: "contractManage/index",
        type: "page",
        style: {
          navigationBarTitleText: "合同签订"
        }
      },
      {
        path: "elevatorVerification/index",
        type: "page",
        style: {
          navigationBarTitleText: "电梯核对"
        }
      },
      {
        path: "privacyPolicy/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "隐私政策"
        }
      },
      {
        path: "realname/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "个人实名认证"
        }
      },
      {
        path: "realname/personalAuth",
        type: "page",
        style: {
          navigationBarTitleText: "个人认证"
        }
      },
      {
        path: "userAgreement/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "用户协议"
        }
      },
      {
        path: "mine/login/index",
        type: "page",
        style: {
          navigationBarTitleText: "登录"
        }
      }
    ]
  },
  {
    root: "echarts",
    pages: [
      {
        path: "index",
        type: "page"
      }
    ]
  }
];
const getAllPages = (key = "needLogin") => {
  const mainPages = [
    ...pages.filter((page) => !key || page[key]).map((page) => __spreadProps(__spreadValues({}, page), {
      path: `/${page.path}`
    }))
  ];
  const subPages = [];
  subPackages.forEach((subPageObj) => {
    const { root } = subPageObj;
    subPageObj.pages.filter((page) => !key || page[key]).forEach((page) => {
      subPages.push(__spreadProps(__spreadValues({}, page), {
        path: `/${root}/${page.path}`
      }));
    });
  });
  const result = [...mainPages, ...subPages];
  return result;
};
const getNeedLoginPages = () => getAllPages("needLogin").map((page) => page.path);
getAllPages("needLogin").map((page) => page.path);
const getEnvBaseUrl = () => {
  let baseUrl = "http://**********:8091/dfloor";
  if (utils_platform.isMpWeixin) {
    const {
      miniProgram: { envVersion }
    } = common_vendor.index.getAccountInfoSync();
    switch (envVersion) {
      case "develop":
        baseUrl = baseUrl;
        break;
      case "trial":
        baseUrl = baseUrl;
        break;
      case "release":
        baseUrl = baseUrl;
        break;
    }
  }
  return baseUrl;
};
exports.getEnvBaseUrl = getEnvBaseUrl;
exports.getNeedLoginPages = getNeedLoginPages;

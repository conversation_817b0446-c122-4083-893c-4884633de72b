<view class="{{['fui-button__wrap', 'data-v-879fd338', ao, ap]}}" style="{{'width:' + aq + ';' + ('height:' + ar) + ';' + ('margin-top:' + as) + ';' + ('margin-right:' + at) + ';' + ('margin-bottom:' + av) + ';' + ('margin-left:' + aw) + ';' + ('border-radius:' + ax) + ';' + ('background:' + ay)}}" bindtouchstart="{{az}}" bindtouchend="{{aA}}" bindtouchcancel="{{aB}}"><button class="{{['fui-button', 'data-v-879fd338', h, i, j, k, l, m]}}" style="{{'width:' + n + ';' + ('height:' + o) + ';' + ('line-height:' + p) + ';' + ('background:' + q) + ';' + ('border-width:' + r) + ';' + ('border-color:' + s) + ';' + ('border-radius:' + t) + ';' + ('font-size:' + v) + ';' + ('color:' + w)}}" loading="{{x}}" form-type="{{y}}" open-type="{{z}}" app-parameter="{{A}}" hoverStopPropagation="{{B}}" lang="{{C}}" sessionFrom="{{D}}" sendMessageTitle="{{E}}" sendMessagePath="{{F}}" sendMessageImg="{{G}}" showMessageCard="{{H}}" groupId="{{I}}" guildId="{{J}}" publicId="{{K}}" dataImId="{{L}}" dataImType="{{M}}" dataGoodsId="{{N}}" dataOrderId="{{O}}" dataBizLine="{{P}}" phoneNumberNoQuotaToast="{{Q}}" bindgetuserinfo="{{R}}" bindgetphonenumber="{{S}}" bindcontact="{{T}}" binderror="{{U}}" bindopensetting="{{V}}" bindchooseavatar="{{W}}" bindlaunchapp="{{X}}" bindagreeprivacyauthorization="{{Y}}" bindaddgroupapp="{{Z}}" bindchooseaddress="{{aa}}" bindchooseinvoicetitle="{{ab}}" bindsubscribe="{{ac}}" bindlogin="{{ad}}" bindim="{{ae}}" disabled="{{af}}" scope="{{ag}}" catchtap="{{ah}}"><text wx:if="{{a}}" class="{{['fui-button__text', 'data-v-879fd338', c && 'fui-btn__gray-color', d && 'fui-text__bold']}}" style="{{'font-size:' + e + ';' + ('line-height:' + f) + ';' + ('color:' + g)}}">{{b}}</text><slot></slot></button><view wx:if="{{ai}}" class="{{['fui-button__thin-border', 'data-v-879fd338', aj, ak]}}" style="{{'border-width:' + al + ';' + ('border-color:' + am) + ';' + ('border-radius:' + an)}}"></view></view>
<wxs src="./wxs/z-paging-wxs.wxs" module="pagingWxs"/>
<view class="{{['data-v-fb5441fe', bI, bJ]}}" style="{{bK}}"><view wx:if="{{a}}" class="zp-safe-area-inset-bottom data-v-fb5441fe"></view><view wx:if="{{b}}" catchtouchmove="{{c}}" class="zp-f2-content data-v-fb5441fe" style="{{d}}"><slot name="f2"/></view><block wx:if="{{e}}"><slot wx:if="{{f}}" name="top"/><view wx:else class="zp-page-top data-v-fb5441fe" catchtouchmove="{{g}}" style="{{h}}"><slot name="top"/></view></block><view class="{{['data-v-fb5441fe', 'zp-view-super', bl && 'zp-scroll-view-super']}}" style="{{bm}}"><view wx:if="{{i}}" class="{{['data-v-fb5441fe', 'zp-page-left', j && 'zp-absoulte']}}"><slot name="left"/></view><view class="{{['data-v-fb5441fe', 'zp-scroll-view-container', bh && 'zp-absoulte']}}" style="{{bi}}"><scroll-view ref="zp-scroll-view" class="{{['data-v-fb5441fe', 'zp-scroll-view', aL && 'zp-scroll-view-absolute', aM && 'zp-scroll-view-hide-scrollbar']}}" style="{{aN}}" scroll-top="{{aO}}" scroll-left="{{aP}}" scroll-x="{{aQ}}" scroll-y="{{aR}}" enable-back-to-top="{{aS}}" show-scrollbar="{{aT}}" scroll-with-animation="{{aU}}" scroll-into-view="{{aV}}" lower-threshold="{{aW}}" upper-threshold="{{5}}" refresher-enabled="{{aX}}" refresher-threshold="{{aY}}" refresher-default-style="{{aZ}}" refresher-background="{{ba}}" refresher-triggered="{{bb}}" bindscroll="{{bc}}" bindscrolltolower="{{bd}}" bindscrolltoupper="{{be}}" bindrefresherrestore="{{bf}}" bindrefresherrefresh="{{bg}}"><view class="zp-paging-touch-view data-v-fb5441fe" bindtouchstart="{{pagingWxs.touchstart}}" bindtouchmove="{{pagingWxs.touchmove}}" bindtouchend="{{pagingWxs.touchend}}" bindtouchcancel="{{pagingWxs.touchend}}" bindmousedown="{{pagingWxs.mousedown}}" bindmousemove="{{pagingWxs.mousemove}}" bindmouseup="{{pagingWxs.mouseup}}" bindmouseleave="{{pagingWxs.mouseleave}}"><view wx:if="{{k}}" class="zp-fixed-bac-view data-v-fb5441fe" style="{{l}}"></view><view class="zp-paging-main data-v-fb5441fe" style="{{ao + ';' + ap}}" change:prop="{{pagingWxs.propObserver}}" prop="{{aq}}" data-refresherThreshold="{{ar}}" data-refresherF2Enabled="{{as}}" data-refresherF2Threshold="{{at}}" data-isIos="{{av}}" data-loading="{{aw}}" data-useChatRecordMode="{{ax}}" data-refresherEnabled="{{ay}}" data-useCustomRefresher="{{az}}" data-pageScrollTop="{{aA}}" data-scrollTop="{{aB}}" data-refresherMaxAngle="{{aC}}" data-refresherNoTransform="{{aD}}" data-refresherAecc="{{aE}}" data-usePageScroll="{{aF}}" data-watchTouchDirectionChange="{{aG}}" data-oldIsTouchmoving="{{aH}}" data-refresherOutRate="{{aI}}" data-refresherPullRate="{{aJ}}" data-hasTouchmove="{{aK}}"><view wx:if="{{m}}" class="zp-custom-refresher-view data-v-fb5441fe" style="{{z}}"><view class="zp-custom-refresher-container data-v-fb5441fe" style="{{y}}"><view wx:if="{{n}}" class="zp-custom-refresher-status-bar-placeholder data-v-fb5441fe" style="{{o}}"/><view class="zp-custom-refresher-slot-view data-v-fb5441fe"><slot wx:if="{{p}}" name="refresher"/></view><slot wx:if="{{r}}" name="refresherComplete"/><slot wx:elif="{{s}}" name="refresherF2"/><z-paging-refresh wx:elif="{{t}}" u-r="refresh" class="zp-custom-refresher-refresh r data-v-fb5441fe" style="{{w}}" u-i="fb5441fe-0" bind:__l="__l" u-p="{{x}}"/></view></view><view class="zp-paging-container data-v-fb5441fe" style="{{an}}"><slot wx:if="{{A}}" name="loading"/><view class="zp-paging-container-content data-v-fb5441fe" style="{{ac + ';' + ad}}"><view wx:if="{{B}}" class="zp-virtual-placeholder data-v-fb5441fe" style="{{C}}"/><slot/><block wx:if="{{D}}"><slot name="header"/><view class="zp-list-container data-v-fb5441fe" style="{{J}}"><block wx:if="{{E}}"><view wx:for="{{F}}" wx:for-item="item" wx:key="d" class="zp-list-cell data-v-fb5441fe" style="{{H}}" id="{{item.c}}" bindtap="{{item.e}}"><view wx:if="{{G}}" class="data-v-fb5441fe">使用兼容模式请在组件源码z-paging.vue第105行中注释这一行，并打开下面一行注释</view><slot wx:else name="{{item.a}}"/></view></block><block wx:else><view wx:for="{{I}}" wx:for-item="item" wx:key="c" class="zp-list-cell data-v-fb5441fe" bindtap="{{item.d}}"><slot name="{{item.a}}"/></view></block></view><slot name="footer"/></block><block wx:if="{{K}}"><view class="data-v-fb5441fe" style="{{Q}}"><slot wx:if="{{L}}" name="chatNoMore"/><block wx:else><slot wx:if="{{M}}" name="chatLoading"/><z-paging-load-more wx:else class="data-v-fb5441fe" binddoClick="{{O}}" u-i="fb5441fe-1" bind:__l="__l" u-p="{{P||''}}"/></block></view></block><view wx:if="{{R}}" class="zp-virtual-placeholder data-v-fb5441fe" style="{{S}}"/><slot wx:if="{{T}}" name="loadingMoreDefault"/><slot wx:elif="{{U}}" name="loadingMoreLoading"/><slot wx:elif="{{V}}" name="loadingMoreNoMore"/><slot wx:elif="{{W}}" name="loadingMoreFail"/><z-paging-load-more wx:elif="{{X}}" class="data-v-fb5441fe" binddoClick="{{Y}}" u-i="fb5441fe-2" bind:__l="__l" u-p="{{Z}}"/><view wx:if="{{aa}}" class="zp-safe-area-placeholder data-v-fb5441fe" style="{{ab}}"/></view><view wx:if="{{ae}}" class="{{['data-v-fb5441fe', 'zp-empty-view', ak && 'zp-empty-view-center']}}" style="{{al + ';' + am}}"><slot wx:if="{{af}}" name="empty"/><z-paging-empty-view wx:else class="data-v-fb5441fe" bindreload="{{ah}}" bindviewClick="{{ai}}" u-i="fb5441fe-3" bind:__l="__l" u-p="{{aj||''}}"/></view></view></view></view></scroll-view></view><view wx:if="{{bj}}" class="{{['data-v-fb5441fe', 'zp-page-right', bk && 'zp-absoulte zp-right']}}"><slot name="right"/></view></view><view class="zp-page-bottom-container data-v-fb5441fe" style="{{'background:' + bz}}"><block wx:if="{{bn}}"><slot wx:if="{{bo}}" name="bottom"/><view wx:else class="zp-page-bottom data-v-fb5441fe" catchtouchmove="{{br}}" style="{{bs}}"><slot name="bottom"/><view wx:if="{{bp}}" class="data-v-fb5441fe" style="{{bq}}"/></view></block><view wx:if="{{bt}}" class="data-v-fb5441fe" style="{{bv}}"/><block wx:if="{{bw}}"><view class="data-v-fb5441fe" style="{{bx}}"/><view class="zp-page-bottom-keyboard-placeholder-animate data-v-fb5441fe" style="{{by}}"/></block></view><view wx:if="{{bA}}" class="{{['data-v-fb5441fe', bE]}}" style="{{bF}}" catchtap="{{bG}}"><slot wx:if="{{bB}}" name="backToTop"/><image wx:else class="{{['zp-back-to-top-img', 'data-v-fb5441fe', bC && 'zp-back-to-top-img-inversion']}}" src="{{bD}}"/></view><view wx:if="{{bH}}" class="zp-loading-fixed data-v-fb5441fe"><slot name="loading"/></view></view>
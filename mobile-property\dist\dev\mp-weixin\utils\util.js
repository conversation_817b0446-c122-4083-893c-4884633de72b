"use strict";
const common_vendor = require("../common/vendor.js");
require("../store/index.js");
require("./index.js");
const showTextToast = (title) => {
  return new Promise((resolve, reject) => {
    common_vendor.index.showToast({
      title,
      icon: "none",
      mask: true,
      duration: 2e3,
      complete: () => {
        resolve(true);
      }
    });
  });
};
exports.showTextToast = showTextToast;

<template>
  <view class="elevator-card">
    <view class="card-header">
      <view class="header-left">
        <view class="title">{{ data.name || '仁恒城市星辰' }}</view>
        <view class="status-badge" :class="statusClass">
          <text class="status-text">{{ statusText }}</text>
        </view>
      </view>
    </view>

    <view class="card-content">
      <view class="info-row">
        <text class="label">小区代码：</text>
        <text class="value">{{ data.code || '38277326262' }}</text>
      </view>

      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-label">楼栋数：</text>
          <text class="stat-value">{{ data.buildingCount || 10 }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">电梯数：</text>
          <text class="stat-value">{{ data.elevatorCount || 5 }}</text>
        </view>
        <view class="button-container" @click="handleView">
          <fui-button text="查看" btnSize="mini" v-if="data.status === 'completed'"></fui-button>
          <fui-button text="去核对" btnSize="mini" type="warning" v-else></fui-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface ElevatorData {
  id?: string | number
  name?: string
  code?: string
  buildingCount?: number
  elevatorCount?: number
  status?: 'completed' | 'pending' // 核对状态
}

interface Props {
  data?: ElevatorData
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
})

const emit = defineEmits<{
  view: [data: ElevatorData]
}>()

// 状态文本
const statusText = computed(() => {
  return props.data?.status === 'completed' ? '已完成' : '待核对'
})

// 状态样式类
const statusClass = computed(() => {
  return props.data?.status === 'completed' ? 'status-completed' : 'status-pending'
})

// 按钮文本
const buttonText = computed(() => {
  return props.data?.status === 'completed' ? '查看' : '核对'
})

// 按钮样式类
const buttonClass = computed(() => {
  return props.data?.status === 'completed' ? 'btn-view' : 'btn-check'
})

const handleView = () => {
  uni.navigateTo({
    url: '/pages-sub/elevatorVerification/detail?id=' + props.data.id,
  })
}
</script>

<style lang="scss" scoped>
.elevator-card {
  background: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 24rpx;

  .header-left {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16rpx;
    .title {
      flex: 1;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }

    .status-badge {
      width: 100rpx;
      padding: 4rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      // 居中
      text-align: center;
      &.status-completed {
        background: #f6ffed;
        color: #52c41a;
        border: 1rpx solid #b7eb8f;

        .status-text {
          color: #52c41a;
        }
      }

      &.status-pending {
        background: #fff7e6;
        color: #fa8c16;
        border: 1rpx solid #ffd591;

        .status-text {
          color: #fa8c16;
        }
      }
    }
  }
}

.card-content {
  .info-row {
    display: flex;
    align-items: center;
    color: var(--fui-color-primary);
    .label {
      font-size: 28rpx;
      line-height: 40rpx;
    }

    .value {
      font-size: 28rpx;
      line-height: 40rpx;
    }
  }

  .stats-row {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .stat-item {
      display: flex;
      align-items: center;

      .stat-label {
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
      }

      .stat-value {
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        font-weight: 500;
      }
    }
  }
}
</style>

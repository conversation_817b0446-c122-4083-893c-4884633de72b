<template>
  <view class="elevator-card">
    <view class="card-header">
      <text class="title">{{ data.name || '仁恒城市星辰' }}</text>
    </view>
    
    <view class="card-content">
      <view class="info-row">
        <text class="label">小区代码：</text>
        <text class="value">{{ data.code || '38277326262' }}</text>
      </view>
      
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-label">楼栋数：</text>
          <text class="stat-value">{{ data.buildingCount || 10 }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">电梯数：</text>
          <text class="stat-value">{{ data.elevatorCount || 5 }}</text>
        </view>
      </view>
    </view>
    
    <view class="card-footer">
      <button class="view-btn" @click="handleView">查看</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface ElevatorData {
  id?: string | number
  name?: string
  code?: string
  buildingCount?: number
  elevatorCount?: number
}

interface Props {
  data?: ElevatorData
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({})
})

const emit = defineEmits<{
  view: [data: ElevatorData]
}>()

const handleView = () => {
  emit('view', props.data)
}
</script>

<style lang="scss" scoped>
.elevator-card {
  background: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 24rpx;
  
  .title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    line-height: 44rpx;
  }
}

.card-content {
  margin-bottom: 30rpx;
  
  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .label {
      font-size: 28rpx;
      color: #666666;
      line-height: 40rpx;
    }
    
    .value {
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }
  }
  
  .stats-row {
    display: flex;
    align-items: center;
    gap: 40rpx;
    
    .stat-item {
      display: flex;
      align-items: center;
      
      .stat-label {
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
      }
      
      .stat-value {
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        font-weight: 500;
      }
    }
  }
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  
  .view-btn {
    background: #1890ff;
    color: #ffffff;
    border: none;
    border-radius: 8rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    line-height: 40rpx;
    font-weight: 500;
    
    &:active {
      background: #096dd9;
    }
  }
}
</style>

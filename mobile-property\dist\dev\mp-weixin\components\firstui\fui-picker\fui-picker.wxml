<view class="fui-picker__wrap data-v-debed8cc" bindtouchend="{{Y}}"><view catchtouchmove="{{a}}" class="{{['fui-picker__mask', 'data-v-debed8cc', b && 'fui-picker__mask-show']}}" style="{{c}}" ref="fui_pkm_ani" catchtap="{{d}}"></view><view key="{{T}}" class="{{['fui-picker__content', 'data-v-debed8cc', U && 'fui-picker__content-dark', V && 'fui-picker__content-show', W && 'fui-picker__radius']}}" style="{{'z-index:' + X}}" ref="fui_pk_ani"><view class="{{['fui-picker__header', 'data-v-debed8cc', p && 'fui-picker__header-dark', q && 'fui-picker__radius']}}" style="{{r}}"><text class="{{['fui-picker__btn-cancel', 'data-v-debed8cc', f]}}" style="{{g}}" catchtap="{{h}}">{{e}}</text><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><text class="{{['fui-picker__title', 'data-v-debed8cc', j]}}" style="{{k}}">{{i}}</text></block><text class="{{['fui-picker__btn-sure', 'data-v-debed8cc', m && 'fui-pk__sure-color']}}" style="{{n}}" catchtap="{{o}}">{{l}}</text></view><view class="data-v-debed8cc" catchtouchstart="{{S}}"><picker-view mask-top-style="{{I}}" mask-bottom-style="{{J}}" mask-style="{{K}}" indicator-style="{{L}}" indicator-class="{{M}}" class="fui-picker__view data-v-debed8cc" style="{{'height:' + N}}" key="{{O}}" value="{{P}}" immediate-change="{{Q}}" bindchange="{{R}}"><picker-view-column class="data-v-debed8cc"><text wx:for="{{s}}" wx:for-item="item" wx:key="b" style="{{t}}" class="{{['fui-picker__text', 'data-v-debed8cc', v && 'fui-picker__color-dark']}}">{{item.a}}</text></picker-view-column><picker-view-column wx:if="{{w}}" class="data-v-debed8cc"><text wx:for="{{x}}" wx:for-item="item" wx:key="b" style="{{y}}" class="{{['fui-picker__text', 'data-v-debed8cc', z && 'fui-picker__color-dark']}}">{{item.a}}</text></picker-view-column><picker-view-column wx:if="{{A}}" class="data-v-debed8cc"><text wx:for="{{B}}" wx:for-item="item" wx:key="b" style="{{C}}" class="{{['fui-picker__text', 'data-v-debed8cc', D && 'fui-picker__color-dark']}}">{{item.a}}</text></picker-view-column><picker-view-column wx:if="{{E}}" class="data-v-debed8cc"><text wx:for="{{F}}" wx:for-item="item" wx:key="b" style="{{G}}" class="{{['fui-picker__text', 'data-v-debed8cc', H && 'fui-picker__color-dark']}}">{{item.a}}</text></picker-view-column></picker-view></view></view></view>
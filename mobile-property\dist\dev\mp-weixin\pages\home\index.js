"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
require("../../store/index.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_button2 + _component_layout_default_uni)();
}
const _easycom_fui_button = () => "../../components/firstui/fui-button/fui-button.js";
if (!Math) {
  _easycom_fui_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    const menuList = common_vendor.ref([
      {
        name: "电梯核对",
        icon: "icon-device",
        bgColor: "bg-_a__a_ff9800_a_",
        url: "/pages-sub/elevatorVerification/index"
      },
      { name: "电梯报修", icon: "icon-repair", bgColor: "bg-_a__a_4caf50_a_" },
      {
        name: "合同管理",
        icon: "icon-contract",
        bgColor: "bg-_a__a_2196f3_a_",
        url: "/pages-sub/contractManage/index"
      },
      { name: "安装记录", icon: "icon-record", bgColor: "bg-_a__a_673ab7_a_" },
      { name: "电梯巡检", icon: "icon-inspect", bgColor: "bg-_a__a_e91e63_a_" }
    ]);
    const handleMenuClick = (item) => {
      if (!item.url) {
        return;
      }
      if (item.url) {
        common_vendor.index.navigateTo({
          url: item.url
        });
      }
    };
    const handleLogin = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/mine/login/index"
      });
    };
    common_vendor.onShow(() => {
      console.log("home---onShow");
      if (isLogined.value)
        ;
    });
    common_vendor.onLoad(() => {
    });
    common_vendor.onUnload(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0,
        b: common_vendor.unref(isLogined)
      }, common_vendor.unref(isLogined) ? {} : {
        c: common_vendor.o(handleLogin),
        d: common_vendor.p({
          btnSize: "mini",
          text: "授权登录"
        })
      }, {
        e: common_vendor.f(menuList.value, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.n(item.bgColor),
            c: common_vendor.t(item.name),
            d: index,
            e: common_vendor.o(($event) => handleMenuClick(item), index)
          };
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2c5296db"]]);
wx.createPage(MiniProgramPage);

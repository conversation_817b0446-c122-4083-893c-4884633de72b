"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "voteMessageItem",
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  setup(__props) {
    const props = __props;
    const content = common_vendor.computed(() => {
      const _text = props.item.status === 1 ? "开始投票啦" : "投票结果公示";
      return `${props.item.voteName}${_text}！`;
    });
    const handleClick = () => {
      if (props.item.status === 1) {
        common_vendor.index.navigateTo({
          url: `/pages-sub/voteNoticeDetail/index?id=${props.item.id}&communityId=${props.item.communityId}`
        });
      } else {
        common_vendor.index.navigateTo({
          url: `/pages-sub/votehasEndResultDetail/index?id=${props.item.id}`
        });
      }
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$2,
        b: common_vendor.t(common_vendor.unref(content)),
        c: common_vendor.t(__props.item.updateTime),
        d: common_vendor.o(handleClick)
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2fe58601"]]);
wx.createComponent(Component);

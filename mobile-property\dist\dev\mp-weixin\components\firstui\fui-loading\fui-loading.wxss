
.fui-loading.data-v-84aa7914 {

		display: flex;
		margin: 0 auto;

		flex-direction: row;
		align-items: center;
		justify-content: center;
}
.fui-loading__fixed.data-v-84aa7914 {

		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);

		z-index: 1008;
}
.fui-loading__wrap.data-v-84aa7914 {
		width: 208rpx;
		height: 208rpx;
		flex-direction: column;
		border-radius: 12rpx;
}
.fui-loading__ani.data-v-84aa7914 {
		width: 64rpx;
		height: 64rpx;
		margin: 0 6px;

		animation: rotate-84aa7914 0.85s linear infinite;

		margin-bottom: 30rpx;
}
.fui-loading__row.data-v-84aa7914 {
		flex: 1;

		width: 100%;

		height: 36rpx;
}
.fui-loading-row__ani.data-v-84aa7914 {
		width: 36rpx;
		height: 36rpx;




		display: block;
		border-radius: 50%;
		animation: rotate-84aa7914 0.85s linear infinite;

		margin-right: 20rpx;
}
.fui-loading__text.data-v-84aa7914 {
		text-align: center;
}
@keyframes rotate-84aa7914 {
0% {
			transform: rotate(0deg);
}
100% {
			transform: rotate(360deg);
}
}
.fui-loading__mask.data-v-84aa7914 {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 1002;
}

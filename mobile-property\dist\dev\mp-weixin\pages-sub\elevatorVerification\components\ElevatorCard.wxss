/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.elevator-card.data-v-559a2a18 {
  background: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.card-header.data-v-559a2a18 {
  margin-bottom: 24rpx;
}
.card-header .header-left.data-v-559a2a18 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}
.card-header .header-left .title.data-v-559a2a18 {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.card-header .header-left .status-badge.data-v-559a2a18 {
  width: 100rpx;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  text-align: center;
}
.card-header .header-left .status-badge.status-completed.data-v-559a2a18 {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}
.card-header .header-left .status-badge.status-completed .status-text.data-v-559a2a18 {
  color: #52c41a;
}
.card-header .header-left .status-badge.status-pending.data-v-559a2a18 {
  background: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #ffd591;
}
.card-header .header-left .status-badge.status-pending .status-text.data-v-559a2a18 {
  color: #fa8c16;
}
.card-content .info-row.data-v-559a2a18 {
  display: flex;
  align-items: center;
  color: var(--fui-color-primary);
}
.card-content .info-row .label.data-v-559a2a18 {
  font-size: 28rpx;
  line-height: 40rpx;
}
.card-content .info-row .value.data-v-559a2a18 {
  font-size: 28rpx;
  line-height: 40rpx;
}
.card-content .stats-row.data-v-559a2a18 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-content .stats-row .stat-item.data-v-559a2a18 {
  display: flex;
  align-items: center;
}
.card-content .stats-row .stat-item .stat-label.data-v-559a2a18 {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
}
.card-content .stats-row .stat-item .stat-value.data-v-559a2a18 {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  font-weight: 500;
}
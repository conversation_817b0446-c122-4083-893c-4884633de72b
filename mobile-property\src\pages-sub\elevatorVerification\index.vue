<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '电梯核对',
  },
}
</route>

<template>
  <z-paging
    ref="paging"
    :auto-hide-loading-after-first-loaded="false"
    loading-full-fixed
    :paging-style="{ 'background-color': '#f7f7f7' }"
    v-model="dataList"
    :auto-show-back-to-top="true"
    @query="queryList"
    :auto="true"
    lower-threshold="150rpx"
  >
    <template #top>
      <view class="bg-[#ffffff]">
        <fui-tabs :tabs="tabs" :short="true" center @change="changeTab"></fui-tabs>
      </view>
    </template>

    <template #loading>
      <fui-loading isMask></fui-loading>
    </template>
    <!-- 设置自己的empty组件，非必须。空数据时会自动展示空数据组件，不需要自己处理 -->
    <template #empty>
      <fui-empty
        :width="386"
        :height="280"
        src="/static/images/img_data_3x.png"
        isFixed
        title="暂无数据"
      ></fui-empty>
    </template>
    <!-- 列表内容 -->
     <view>
      
     </view>
    <ElevatorCard v-for="item in dataList" :key="item.id" :data="item" @view="handleViewElevator" />

    <template #bottom>
      <safe-bottom-zone></safe-bottom-zone>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import safeBottomZone from '@/components/common/safeBottomZone.vue'
import ElevatorCard from './components/ElevatorCard.vue'

onLoad(async () => {})

const userStore = useUserStore()
const { userInfo, isLogined } = storeToRefs(userStore)

const dataList = ref([])

const tabs = ref(['全部', '已完成', '待核对'])

const activeCurrent = ref<number>(0)

const changeTab = (e: any) => {
  const { index } = e
  activeCurrent.value = index
  console.log('index', index)
  paging.value?.reload()
}

// 处理查看电梯详情
const handleViewElevator = (data: any) => {
  console.log('查看电梯详情:', data)
  // 这里可以跳转到详情页面或者打开弹窗
  uni.showToast({
    title: `查看${data.name || '电梯'}详情`,
    icon: 'none',
  })
}

const paging = ref(null)

const queryList = async (pageNo: number, pageSize: number) => {
  // 全选设置成false
  const params = {
    pageNo,
    pageSize,
  }

  console.log('params', params)

  // 模拟电梯数据
  const mockData = [
    {
      id: 1,
      name: '仁恒城市星辰',
      code: '38277326262',
      buildingCount: 10,
      elevatorCount: 5,
    },
    {
      id: 2,
      name: '万科城市花园',
      code: '38277326263',
      buildingCount: 8,
      elevatorCount: 12,
    },
    {
      id: 3,
      name: '绿地中央广场',
      code: '38277326264',
      buildingCount: 15,
      elevatorCount: 20,
    },
  ]

  // 此处请求仅为演示，请替换为自己项目中的请求
  paging.value.complete(mockData, true)
}
</script>

<style lang="scss" scoped></style>

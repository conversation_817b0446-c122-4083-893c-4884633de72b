<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '电梯核对',
  },
}
</route>

<template>
  <view class="">
    <z-paging
      ref="paging"
      :auto-hide-loading-after-first-loaded="false"
      loading-full-fixed
      :paging-style="{ 'background-color': '#f7f7f7' }"
      v-model="dataList"
      :auto-show-back-to-top="true"
      :back-to-top-bottom="200"
      @query="queryList"
      :auto="true"
      lower-threshold="150rpx"
    >
      <template #top>
        <view></view>
        <fui-tabs :tabs="tabs" :current="activeCurrent" @change="changeTab"></fui-tabs>
      </template>

      <template #loading>
        <fui-loading isMask></fui-loading>
      </template>
      <!-- 设置自己的empty组件，非必须。空数据时会自动展示空数据组件，不需要自己处理 -->
      <template #empty>
        <fui-empty
          :width="386"
          :height="280"
          src="/static/images/img_data_3x.png"
          isFixed
          title="暂无数据"
        ></fui-empty>
      </template>
      <template #bottom>
        <safe-bottom-zone></safe-bottom-zone>
      </template>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import safeBottomZone from '@/components/common/safeBottomZone.vue'

onLoad(async () => {})

const userStore = useUserStore()
const { userInfo, isLogined } = storeToRefs(userStore)

const dataList = ref([])

const tabs = ref(['全部', '已完成', '待核对'])

const activeCurrent = ref<number>(0)

const changeTab = (e) => {
  const { index, item } = e
  activeCurrent.value = index
  console.log('index', index)
}

const paging = ref(null)

const queryList = async (pageNo, pageSize) => {
  // 全选设置成false
  const params = {
    pageNo,
    pageSize,
  }

  console.log('params', params)
  //   paging.value.complete(false)
  // 此处请求仅为演示，请替换为自己项目中的请求
  paging.value.complete([{ id: 1 }, { id: 2 }, { id: 3 }], true)
}
</script>

<style lang="scss" scoped></style>

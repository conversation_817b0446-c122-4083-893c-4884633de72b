"use strict";
const common_vendor = require("../common/vendor.js");
require("../store/index.js");
const utils_index = require("../utils/index.js");
const store_user = require("../store/user.js");
const loginRoute = "/pages/login/index";
const isLogined = () => {
  const userStore = store_user.useUserStore();
  return userStore.isLogined;
};
const navigateToInterceptor = {
  // 注意，这里的url是 '/' 开头的，如 '/pages/index/index'，跟 'pages.json' 里面的 path 不同
  invoke({ url }) {
    const path = url.split("?")[0];
    let needLoginPages = [];
    {
      needLoginPages = utils_index.getNeedLoginPages();
    }
    const isNeedLogin = needLoginPages.includes(path);
    if (!isNeedLogin) {
      return true;
    }
    const hasLogin = isLogined();
    if (hasLogin) {
      return true;
    }
    const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(url)}`;
    common_vendor.index.navigateTo({ url: redirectRoute });
    return false;
  }
};
const routeInterceptor = {
  install() {
    common_vendor.index.addInterceptor("navigateTo", navigateToInterceptor);
    common_vendor.index.addInterceptor("reLaunch", navigateToInterceptor);
    common_vendor.index.addInterceptor("redirectTo", navigateToInterceptor);
    common_vendor.index.addInterceptor("switchTab", navigateToInterceptor);
  }
};
exports.routeInterceptor = routeInterceptor;

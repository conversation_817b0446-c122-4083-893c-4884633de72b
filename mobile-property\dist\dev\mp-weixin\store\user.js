"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const service_app_miniProgramLogin = require("../service/app/miniProgramLogin.js");
require("../utils/http.js");
const common_vendor = require("../common/vendor.js");
const initState = { username: "", realname: "", phone: "" };
const useUserStore = common_vendor.defineStore(
  "user",
  () => {
    const userInfo = common_vendor.ref(__spreadValues({}, initState));
    const setUserInfo = (val) => {
      userInfo.value = __spreadValues(__spreadValues({}, userInfo.value), val);
    };
    const setToken = (val) => {
      userInfo.value.token = val;
    };
    const clearUserInfo = () => {
      userInfo.value = __spreadValues({}, initState);
    };
    const reset = () => {
      userInfo.value = __spreadValues({}, initState);
    };
    const isLogined = common_vendor.computed(() => !!userInfo.value.token);
    const checkUserInfo = () => __async(exports, null, function* () {
      try {
        if (!userInfo.value.id)
          return;
        const res = yield service_app_miniProgramLogin.getAccountInfo({});
        console.log("res1111", res);
        if (res.code === 200) {
          const { realname, username, phone, isRealAuth } = res.result;
          userInfo.value = __spreadProps(__spreadValues({}, userInfo.value), {
            realname,
            username,
            phone,
            isRealAuth
          });
        }
        return res;
      } catch (error) {
        console.error("获取用户信息失败:", error);
        return error;
      }
    });
    return {
      userInfo,
      setUserInfo,
      setToken,
      clearUserInfo,
      isLogined,
      reset,
      checkUserInfo
    };
  },
  {
    persist: true
  }
);
exports.useUserStore = useUserStore;

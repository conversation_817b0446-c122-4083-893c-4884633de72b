<view class="{{['data-v-b55bdf15', 'zp-container', t && 'zp-container-fixed']}}" style="{{v}}" bindtap="{{w}}"><view class="zp-main data-v-b55bdf15"><image wx:if="{{a}}" class="{{['data-v-b55bdf15', b && 'zp-main-image-rpx', c && 'zp-main-image-px']}}" style="{{d}}" src="{{e}}"/><image wx:else class="{{['data-v-b55bdf15', f && 'zp-main-image-rpx', g && 'zp-main-image-px']}}" mode="aspectFit" style="{{h}}" src="{{i}}"/><text class="{{['zp-main-title', 'data-v-b55bdf15', k && 'zp-main-title-rpx', l && 'zp-main-title-px']}}" style="{{m}}">{{j}}</text><text wx:if="{{n}}" class="{{['data-v-b55bdf15', 'zp-main-error-btn', p && 'zp-main-error-btn-rpx', q && 'zp-main-error-btn-px']}}" style="{{r}}" catchtap="{{s}}">{{o}}</text></view></view>
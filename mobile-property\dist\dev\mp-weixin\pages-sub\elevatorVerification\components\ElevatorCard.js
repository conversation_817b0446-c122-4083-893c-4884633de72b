"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_fui_button2 = common_vendor.resolveComponent("fui-button");
  _easycom_fui_button2();
}
const _easycom_fui_button = () => "../../../components/firstui/fui-button/fui-button.js";
if (!Math) {
  _easycom_fui_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "ElevatorCard",
  props: {
    data: { default: () => ({}) }
  },
  emits: ["view"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const statusText = common_vendor.computed(() => {
      var _a;
      return ((_a = props.data) == null ? void 0 : _a.status) === "completed" ? "已完成" : "待核对";
    });
    const statusClass = common_vendor.computed(() => {
      var _a;
      return ((_a = props.data) == null ? void 0 : _a.status) === "completed" ? "status-completed" : "status-pending";
    });
    common_vendor.computed(() => {
      var _a;
      return ((_a = props.data) == null ? void 0 : _a.status) === "completed" ? "查看" : "核对";
    });
    common_vendor.computed(() => {
      var _a;
      return ((_a = props.data) == null ? void 0 : _a.status) === "completed" ? "btn-view" : "btn-check";
    });
    const handleView = () => {
      common_vendor.index.navigateTo({
        url: "/pages-sub/elevatorVerification/detail?id=" + props.data.id
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(_ctx.data.name || "仁恒城市星辰"),
        b: common_vendor.t(common_vendor.unref(statusText)),
        c: common_vendor.n(common_vendor.unref(statusClass)),
        d: common_vendor.t(_ctx.data.code || "38277326262"),
        e: common_vendor.t(_ctx.data.buildingCount || 10),
        f: common_vendor.t(_ctx.data.elevatorCount || 5),
        g: _ctx.data.status === "completed"
      }, _ctx.data.status === "completed" ? {
        h: common_vendor.p({
          text: "查看",
          btnSize: "mini"
        })
      } : {
        i: common_vendor.p({
          text: "去核对",
          btnSize: "mini",
          type: "warning"
        })
      }, {
        j: common_vendor.o(handleView)
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-559a2a18"]]);
wx.createComponent(Component);

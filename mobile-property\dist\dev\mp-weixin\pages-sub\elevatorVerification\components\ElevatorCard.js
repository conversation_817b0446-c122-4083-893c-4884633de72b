"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "ElevatorCard",
  props: {
    data: { default: () => ({}) }
  },
  emits: ["view"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const handleView = () => {
      emit("view", props.data);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(_ctx.data.name || "仁恒城市星辰"),
        b: common_vendor.t(_ctx.data.code || "38277326262"),
        c: common_vendor.t(_ctx.data.buildingCount || 10),
        d: common_vendor.t(_ctx.data.elevatorCount || 5),
        e: common_vendor.o(handleView)
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-559a2a18"]]);
wx.createComponent(Component);

/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/home/<USER>" |
       "/pages/message/index" |
       "/pages/mine/index" |
       "/pages-sub/authNotice/index" |
       "/pages-sub/contractManage/index" |
       "/pages-sub/elevatorVerification/detail" |
       "/pages-sub/elevatorVerification/index" |
       "/pages-sub/privacyPolicy/index" |
       "/pages-sub/realname/index" |
       "/pages-sub/realname/personalAuth" |
       "/pages-sub/userAgreement/index" |
       "/pages-sub/mine/login/index" |
       "/echarts/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/home/<USER>" | "/pages/message/index" | "/pages/mine/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}

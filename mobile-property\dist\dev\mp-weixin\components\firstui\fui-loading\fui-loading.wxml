<view class="{{['data-v-84aa7914', q && 'fui-loading__mask']}}" style="{{'background-color:' + r}}"><view wx:if="{{a}}" class="{{['fui-loading', 'fui-loading__wrap', 'data-v-84aa7914', g && 'fui-loading__fixed']}}" style="{{'background-color:' + h + ';' + ('position:' + i)}}"><image ref="fui_loading" class="fui-loading__ani data-v-84aa7914" src="{{b}}"></image><text class="fui-loading__text data-v-84aa7914" style="{{'color:' + d + ';' + ('font-size:' + e) + ';' + ('line-height:' + f)}}">{{c}}</text></view><view wx:else class="{{['fui-loading', 'fui-loading__row', 'data-v-84aa7914', o && 'fui-loading__fixed']}}" style="{{'position:' + p}}"><image ref="fui_loading" class="fui-loading-row__ani data-v-84aa7914" src="{{j}}"></image><text class="fui-loading__text data-v-84aa7914" style="{{'color:' + l + ';' + ('font-size:' + m) + ';' + ('line-height:' + n)}}">{{k}}</text></view></view>
"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Array) {
  const _easycom_fui_tag2 = common_vendor.resolveComponent("fui-tag");
  _easycom_fui_tag2();
}
const _easycom_fui_tag = () => "../../../components/firstui/fui-tag/fui-tag.js";
if (!Math) {
  _easycom_fui_tag();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "myContractItem",
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          text: "已签约",
          theme: "plain",
          type: "success",
          padding: ["8rpx", "16rpx"]
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2ca9a90c"]]);
wx.createComponent(Component);

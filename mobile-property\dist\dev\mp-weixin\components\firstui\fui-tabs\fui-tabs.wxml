<scroll-view class="{{['fui-tabs__scrollbox', 'data-v-e6d1c91f', h && 'fui-tabs__fixed', i && 'fui-tabs__sticky']}}" scroll-with-animation="{{true}}" scroll-x="{{j}}" show-scrollbar="{{false}}" scroll-into-view="{{k}}" style="{{'background:' + l + ';' + ('z-index:' + m) + ';' + ('top:' + n)}}"><view class="{{['fui-scroll__view', 'data-v-e6d1c91f', g && 'fui-tabs__full']}}"><view wx:for="{{a}}" wx:for-item="tab" wx:key="F" class="{{['fui-tabs__item', 'data-v-e6d1c91f', d && 'fui-tabs__full']}}" style="{{'padding-left:' + e + ';' + ('padding-right:' + f)}}" id="{{tab.G}}" bindtap="{{tab.H}}"><view class="{{['fui-tabs__text-wrap', 'data-v-e6d1c91f', tab.D && 'fui-tabs__wrap-disabled', tab.E && 'fui-tabs__item-column']}}" style="{{'height:' + c}}"><view wx:if="{{b}}" class="{{['fui-tabs__line-wrap', 'data-v-e6d1c91f', tab.h && 'fui-tabs__line-center']}}" style="{{'bottom:' + tab.i + ';' + ('left:' + tab.j) + ';' + ('right:' + tab.k)}}"><view class="{{['fui-tabs__ac-line', 'data-v-e6d1c91f', tab.a && 'fui-tabs__line-short', tab.b && 'fui-tabs__full', tab.c && 'fui-tabs__slider-color']}}" style="{{'height:' + tab.d + ';' + ('background:' + tab.e) + ';' + ('border-radius:' + tab.f) + ';' + ('transform:' + tab.g)}}"></view></view><image wx:if="{{tab.l}}" class="{{['fui-tabs__icon', 'data-v-e6d1c91f', tab.m && 'fui-tabs__icon-column']}}" src="{{tab.n}}"></image><view class="{{['fui-tabs__text', 'data-v-e6d1c91f', tab.x && 'fui-tabs__selected-color', tab.y && 'fui-tabs__text-color']}}" style="{{'font-size:' + tab.z + ';' + ('color:' + tab.A) + ';' + ('font-weight:' + tab.B) + ';' + ('transform:' + tab.C)}}">{{tab.o}}<text wx:if="{{tab.p}}" class="{{['data-v-e6d1c91f', tab.r && 'fui-tabs__badge-color', tab.s && 'fui-tabs__badge-dot', tab.t && 'fui-tabs__badge']}}" style="{{'color:' + tab.v + ';' + ('background:' + tab.w)}}">{{tab.q}}</text></view></view></view></view></scroll-view>
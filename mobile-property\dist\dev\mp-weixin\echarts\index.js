"use strict";
const common_vendor = require("../common/vendor.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
if (!Math) {
  BaseCharts();
}
const BaseCharts = () => "./components/BaseCharts.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const options = common_vendor.ref({
      grid: {
        left: "3%",
        right: "6%",
        bottom: "3%",
        top: "8%",
        containLabel: true
      },
      tooltip: {
        trigger: "axis",
        confine: true
      },
      xAxis: {
        boundaryGap: false,
        type: "category",
        axisLabel: {
          color: "#999999"
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        data: ["03:00", "06:00", "07:00", "12:00", "15:00", "18:00", "21:00"]
      },
      yAxis: {
        type: "value",
        axisLabel: {
          color: "#999999"
        }
      },
      series: [
        {
          smooth: true,
          symbolSize: 0,
          lineStyle: {
            normal: {
              color: "#8dadfa"
              // 线条颜色
            }
          },
          data: [150, 224, 218, 135, 147, 113, 211],
          type: "line"
        }
      ]
    });
    setTimeout(() => {
      options.value.series[0].data = [15, 24, 18, 13, 47, 13, 21];
    }, 3e3);
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          options: options.value
        })
      };
    };
  }
};
wx.createPage(_sfc_main);

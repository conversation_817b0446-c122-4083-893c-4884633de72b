/* [z-paging]公共css*/
.z-paging-content.data-v-fb5441fe {
	position: relative;
	flex-direction: column;

	overflow: hidden;
}
.z-paging-content-full.data-v-fb5441fe {

	display: flex;
	width: 100%;
	height: 100%;
}
.z-paging-content-fixed.data-v-fb5441fe, .zp-loading-fixed.data-v-fb5441fe {
	position: fixed;

	height: auto;
	width: auto;

	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
}
.zp-f2-content.data-v-fb5441fe {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	background-color: white;
}
.zp-page-top.data-v-fb5441fe, .zp-page-bottom.data-v-fb5441fe {

	width: auto;

	position: fixed;
	left: 0;
	right: 0;
	z-index: 999;
}
.zp-page-left.data-v-fb5441fe, .zp-page-right.data-v-fb5441fe {

	height: 100%;
}
.zp-scroll-view-super.data-v-fb5441fe {
	flex: 1;
	overflow: hidden;
	position: relative;
}
.zp-view-super.data-v-fb5441fe {

	display: flex;

	flex-direction: row;
}
.zp-scroll-view-container.data-v-fb5441fe, .zp-scroll-view.data-v-fb5441fe {
	position: relative;

	height: 100%;
	width: 100%;
}
.zp-absoulte.data-v-fb5441fe {

	position: absolute;
	top: 0;
	width: auto;
}
.zp-scroll-view-absolute.data-v-fb5441fe {
	position: absolute;
	top: 0;
	left: 0;
}
.zp-scroll-view-hide-scrollbar.data-v-fb5441fe ::-webkit-scrollbar {
	display: none;
	-webkit-appearance: none;
	width: 0 !important;
	height: 0 !important;
	background: transparent;
}
.zp-paging-touch-view.data-v-fb5441fe {
	width: 100%;
	height: 100%;
	position: relative;
}
.zp-fixed-bac-view.data-v-fb5441fe {
	position: absolute;
	width: 100%;
	top: 0;
	left: 0;
	height: 200px;
}
.zp-paging-main.data-v-fb5441fe {
	height: 100%;

	display: flex;

	flex-direction: column;
}
.zp-paging-container.data-v-fb5441fe {
	flex: 1;
	position: relative;

	display: flex;

	flex-direction: column;
}
.zp-chat-record-loading-custom-image.data-v-fb5441fe {
	width: 35rpx;
	height: 35rpx;

	animation: loading-flower-fb5441fe 1s linear infinite;
}
.zp-page-bottom-keyboard-placeholder-animate.data-v-fb5441fe {
	transition-property: height;
	transition-duration: 0.15s;

	will-change: height;
}
.zp-custom-refresher-container.data-v-fb5441fe {
	overflow: hidden;
}
.zp-custom-refresher-refresh.data-v-fb5441fe {

	display: block;
}
.zp-back-to-top.data-v-fb5441fe {
	z-index: 999;
	position: absolute;
	bottom: 0rpx;
	transition-duration: .3s;
	transition-property: opacity;
}
.zp-back-to-top-rpx.data-v-fb5441fe {
	width: 76rpx;
	height: 76rpx;
	bottom: 0rpx;
	right: 25rpx;
}
.zp-back-to-top-px.data-v-fb5441fe {
	width: 38px;
	height: 38px;
	bottom: 0px;
	right: 13px;
}
.zp-back-to-top-show.data-v-fb5441fe {
	opacity: 1;
}
.zp-back-to-top-hide.data-v-fb5441fe {
	opacity: 0;
}
.zp-back-to-top-img.data-v-fb5441fe {

	width: 100%;
	height: 100%;




	z-index: 999;
}
.zp-back-to-top-img-inversion.data-v-fb5441fe {
	transform: rotate(180deg);
}
.zp-empty-view.data-v-fb5441fe {



	flex: 1;
}
.zp-empty-view-center.data-v-fb5441fe {

	display: flex;

	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.zp-loading-fixed.data-v-fb5441fe {
	z-index: 9999;
}
.zp-safe-area-inset-bottom.data-v-fb5441fe {
	position: absolute;

	height: env(safe-area-inset-bottom);
}
.zp-n-refresh-container.data-v-fb5441fe {

	display: flex;

	justify-content: center;
	width: 750rpx;
}
.zp-n-list-container.data-v-fb5441fe{

	display: flex;

	flex-direction: row;
	flex: 1;
}

/* [z-paging]公用的静态css资源 */
.zp-line-loading-image.data-v-fb5441fe {

	animation: loading-flower-fb5441fe 1s steps(12) infinite;

	color: #666666;
}
.zp-line-loading-image-rpx.data-v-fb5441fe {
	margin-right: 8rpx;
	width: 34rpx;
	height: 34rpx;
}
.zp-line-loading-image-px.data-v-fb5441fe {
	margin-right: 4px;
	width: 17px;
	height: 17px;
}
.zp-loading-image-ios-rpx.data-v-fb5441fe {
	width: 40rpx;
	height: 40rpx;
}
.zp-loading-image-ios-px.data-v-fb5441fe {
	width: 20px;
	height: 20px;
}
.zp-loading-image-android-rpx.data-v-fb5441fe {
	width: 34rpx;
	height: 34rpx;
}
.zp-loading-image-android-px.data-v-fb5441fe {
	width: 17px;
	height: 17px;
}
@keyframes loading-flower-fb5441fe {
0% {
		transform: rotate(0deg);
}
to {
		transform: rotate(1turn);
}
}



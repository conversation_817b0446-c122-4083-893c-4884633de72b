
.zp-container.data-v-b55bdf15{

		display: flex;

		align-items: center;
		justify-content: center;
}
.zp-container-fixed.data-v-b55bdf15 {

		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
}
.zp-main.data-v-b55bdf15{

		display: flex;

		flex-direction: column;
		align-items: center;
        padding: 50rpx 0rpx;
}
.zp-main-image-rpx.data-v-b55bdf15 {
		width: 240rpx;
		height: 240rpx;
}
.zp-main-image-px.data-v-b55bdf15 {
		width: 120px;
		height: 120px;
}
.zp-main-title.data-v-b55bdf15 {
		color: #aaaaaa;
		text-align: center;
}
.zp-main-title-rpx.data-v-b55bdf15 {
		font-size: 28rpx;
		margin-top: 10rpx;
		padding: 0rpx 20rpx;
}
.zp-main-title-px.data-v-b55bdf15 {
		font-size: 14px;
		margin-top: 5px;
		padding: 0px 10px;
}
.zp-main-error-btn.data-v-b55bdf15 {
		border: solid 1px #dddddd;
		color: #aaaaaa;
}
.zp-main-error-btn-rpx.data-v-b55bdf15 {
		font-size: 28rpx;
		padding: 8rpx 24rpx;
		border-radius: 6rpx;
		margin-top: 50rpx;
}
.zp-main-error-btn-px.data-v-b55bdf15 {
		font-size: 14px;
		padding: 4px 12px;
		border-radius: 3px;
		margin-top: 25px;
}

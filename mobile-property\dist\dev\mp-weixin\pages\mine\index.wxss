/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-content.data-v-9023ef44 {
  padding: 12rpx 24rpx;
  margin: 0rpx 20rpx 0rpx 20rpx;
  background-color: #fff;
  border-radius: 24rpx;
}
.fui-list__item.data-v-9023ef44 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.fui-align__center.data-v-9023ef44 {
  display: flex;
  align-items: center;
}
.fui-text__explain.data-v-9023ef44 {
  font-size: 28rpx;
  color: #7f7f7f;
  flex-shrink: 0;
}
.fui-list__icon.data-v-9023ef44 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}
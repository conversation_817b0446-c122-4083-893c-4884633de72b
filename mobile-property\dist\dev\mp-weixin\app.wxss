/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.test {
  margin-top:32rpx;margin-left:32rpx;
  padding-top: 4px;
  color: red;
}
 page,::before,::after{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}::backdrop{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}.container{width:100%;}.center{display:flex;align-items:center;justify-content:center;}@media (min-width: 640px){.container{max-width:640px;}}@media (min-width: 768px){.container{max-width:768px;}}@media (min-width: 1024px){.container{max-width:1024px;}}@media (min-width: 1280px){.container{max-width:1280px;}}@media (min-width: 1536px){.container{max-width:1536px;}}.top-_a_-110rpx_a_{top:-110rpx;}.h-100_a_,.h100_a_{height:100%;}.h90_a_{height:90%;}.w-100_a_,.w100_a_{width:100%;}.rounded-_a_50_a__a_{border-radius:50%;}.bg-_a__a_2196f3_a_{--un-bg-opacity:1;background-color:rgb(33 150 243 / var(--un-bg-opacity));}.bg-_a__a_3a56e4_a_{--un-bg-opacity:1;background-color:rgb(58 86 228 / var(--un-bg-opacity));}.bg-_a__a_4caf50_a_{--un-bg-opacity:1;background-color:rgb(76 175 80 / var(--un-bg-opacity));}.bg-_a__a_673ab7_a_{--un-bg-opacity:1;background-color:rgb(103 58 183 / var(--un-bg-opacity));}.bg-_a__a_e91e63_a_{--un-bg-opacity:1;background-color:rgb(233 30 99 / var(--un-bg-opacity));}.bg-_a__a_eee_a_{--un-bg-opacity:1;background-color:rgb(238 238 238 / var(--un-bg-opacity));}.bg-_a__a_f0f9eb_a_{--un-bg-opacity:1;background-color:rgb(240 249 235 / var(--un-bg-opacity));}.bg-_a__a_f7f7f7_a_{--un-bg-opacity:1;background-color:rgb(247 247 247 / var(--un-bg-opacity));}.bg-_a__a_f8a145_a_{--un-bg-opacity:1;background-color:rgb(248 161 69 / var(--un-bg-opacity));}.bg-_a__a_ff9800_a_{--un-bg-opacity:1;background-color:rgb(255 152 0 / var(--un-bg-opacity));}.bg-_a__a_ffffff_a_{--un-bg-opacity:1;background-color:rgb(255 255 255 / var(--un-bg-opacity));}.py-0_a_5{padding-top:4rpx;padding-bottom:4rpx;}.text-_a__a_67c23a_a_{--un-text-opacity:1;color:rgb(103 194 58 / var(--un-text-opacity));}.text-_a__a_7F7F7F_a_{--un-text-opacity:1;color:rgb(127 127 127 / var(--un-text-opacity));}.text-_a__a_ffffff_a_{--un-text-opacity:1;color:rgb(255 255 255 / var(--un-text-opacity));}.text-_a_666{--un-text-opacity:1;color:rgb(102 102 102 / var(--un-text-opacity));}.visible{visibility:visible;}.absolute{position:absolute;}.fixed{position:fixed;}.relative{position:relative;}.sticky{position:sticky;}.static{position:static;}.bottom-0{bottom:0;}.bottom-200rpx{bottom:200rpx;}.bottom-26rpx{bottom:26rpx;}.left-0{left:0;}.left-40rpx{left:40rpx;}.right-0{right:0;}.top-0{top:0;}.z-0{z-index:0;}.z-10{z-index:10;}.z-2{z-index:2;}.z-7{z-index:7;}.z-8{z-index:8;}.grid{display:grid;}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr));}.grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr));}.grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr));}.my-12rpx{margin-top:12rpx;margin-bottom:12rpx;}.my2{margin-top:16rpx;margin-bottom:16rpx;}.mb-20rpx{margin-bottom:20rpx;}.ml-1{margin-left:8rpx;}.ml-3{margin-left:24rpx;}.ml10rpx{margin-left:10rpx;}.ml32rpx{margin-left:32rpx;}.mr-2{margin-right:16rpx;}.mr12rpx{margin-right:12rpx;}.ms{margin-inline-start:32rpx;}.mt-1{margin-top:8rpx;}.mt-2,.mt2{margin-top:16rpx;}.mt-3{margin-top:24rpx;}.mt-4{margin-top:32rpx;}.mt10rpx{margin-top:10rpx;}.mt12rpx{margin-top:12rpx;}.mt20rpx{margin-top:20rpx;}.mt38rpx{margin-top:38rpx;}.mt40rpx{margin-top:40rpx;}.mt60rpx{margin-top:60rpx;}.mt6rpx{margin-top:6rpx;}.mt80rpx{margin-top:80rpx;}.inline{display:inline;}.block{display:block;}.inline-block{display:inline-block;}.hidden{display:none;}.h-12{height:96rpx;}.h-14{height:112rpx;}.h-2rpx{height:2rpx;}.h-300rpx{height:300rpx;}.h-400rpx,.h400rpx{height:400rpx;}.h-42rpx{height:42rpx;}.h-500rpx,.h500rpx{height:500rpx;}.h-8{height:64rpx;}.h128rpx{height:128rpx;}.h140rpx{height:140rpx;}.h1px{height:1px;}.h200rpx{height:200rpx;}.w-116rpx{width:116rpx;}.w-12{width:96rpx;}.w-136rpx{width:136rpx;}.w-14{width:112rpx;}.w-200rpx,.w200rpx{width:200rpx;}.w-8{width:64rpx;}.w128rpx{width:128rpx;}.wxs{width:640rpx;}.flex{display:flex;}.inline-flex{display:inline-flex;}.flex-1{flex:1 1 0%;}.flex-shrink{flex-shrink:1;}.flex-col{flex-direction:column;}.flex-wrap{flex-wrap:wrap;}.transform{transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.items-start{align-items:flex-start;}.items-end{align-items:flex-end;}.items-center{align-items:center;}.justify-center{justify-content:center;}.justify-between{justify-content:space-between;}.gap-2{gap:16rpx;}.gap-20rpx{gap:20rpx;}.overflow-y-auto{overflow-y:auto;}.text-ellipsis{text-overflow:ellipsis;}.break-all{word-break:break-all;}.b,.border{border-width:1px;}.rounded{border-radius:8rpx;}.rounded-10rpx{border-radius:10rpx;}.rounded-12rpx{border-radius:12rpx;}.rounded-full{border-radius:9999px;}.rounded-lg{border-radius:16rpx;}.rounded-t-12rpx{border-top-left-radius:12rpx;border-top-right-radius:12rpx;}.bg-teal-600{--un-bg-opacity:1;background-color:rgb(13 148 136 / var(--un-bg-opacity));}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255 / var(--un-bg-opacity));}.bg-opacity-20{--un-bg-opacity:0.2;}.p-3{padding:24rpx;}.px,.px-4,.px32rpx{padding-left:32rpx;padding-right:32rpx;}.px-2{padding-left:16rpx;padding-right:16rpx;}.px-3{padding-left:24rpx;padding-right:24rpx;}.px20rpx{padding-left:20rpx;padding-right:20rpx;}.px40rpx{padding-left:40rpx;padding-right:40rpx;}.px50rpx{padding-left:50rpx;padding-right:50rpx;}.py-1{padding-top:8rpx;padding-bottom:8rpx;}.py-3{padding-top:24rpx;padding-bottom:24rpx;}.py20rpx{padding-top:20rpx;padding-bottom:20rpx;}.pb-2{padding-bottom:16rpx;}.pl16rpx{padding-left:16rpx;}.pr{padding-right:32rpx;}.pt20rpx{padding-top:20rpx;}.text-center{text-align:center;}.text-24rpx{font-size:24rpx;}.text-26rpx{font-size:26rpx;}.text-2xl{font-size:48rpx;line-height:64rpx;}.text-30rpx{font-size:30rpx;}.text-32rpx{font-size:32rpx;}.text-34rpx{font-size:34rpx;}.text-lg{font-size:36rpx;line-height:56rpx;}.text-sm{font-size:28rpx;line-height:40rpx;}.text-xl{font-size:40rpx;line-height:56rpx;}.text-xs{font-size:24rpx;line-height:32rpx;}.text-gray-400{--un-text-opacity:1;color:rgb(156 163 175 / var(--un-text-opacity));}.text-gray-500{--un-text-opacity:1;color:rgb(107 114 128 / var(--un-text-opacity));}.text-gray-700{--un-text-opacity:1;color:rgb(55 65 81 / var(--un-text-opacity));}.text-white{--un-text-opacity:1;color:rgb(255 255 255 / var(--un-text-opacity));}.font-bold{font-weight:700;}.blur{--un-blur:blur(8px);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.ease-in-out{transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);} 
/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
  FirstUI组件内置的基础变量
  1.如果你是组件使用者，你可以通过修改这些变量的值来定制自己的组件主题，实现自定义主题功能
  2.如果全局修改需要在项目根目录下App.vue文件中引入此css文件
  3.如果组件中有props属性是针对颜色设置（默认为空值），则优先级：props变量（如果有传值）> 全局主题色
*/
page {
  /* 行为相关颜色 */
  --fui-color-primary: #007eff;
  --fui-color-success: #09be4f;
  --fui-color-warning: #ffb703;
  --fui-color-danger: #ff2b2b;
  --fui-color-purple: #6831ff;
  /* 文字基本颜色、其他辅助色 */
  /* 用于重量级文字信息、标题 */
  --fui-color-title: #181818;
  /* 用于普通级段落信息、引导词 */
  --fui-color-section: #333333;
  /* 用于次要标题内容 */
  --fui-color-subtitle: #7f7f7f;
  /* 用于底部标签、描述、次要文字信息 */
  --fui-color-label: #b2b2b2;
  /* 用于辅助、次要信息、禁用文字等。如：待输入状态描述文字，已点击按钮文字 */
  --fui-color-minor: #cccccc;
  --fui-color-white: #ffffff;
  /* 链接颜色 */
  --fui-color-link: #465cff;
  /* 背景颜色 */
  --fui-bg-color: #ffffff;
  /* 页面背景底色 */
  --fui-bg-color-grey: #f1f4fa;
  /* 内容模块底色 */
  --fui-bg-color-content: #f8f8f8;
  --fui-bg-color-red: rgba(255, 43, 43, 0.05);
  --fui-bg-color-yellow: rgba(255, 183, 3, 0.1);
  --fui-bg-color-purple: rgba(104, 49, 255, 0.05);
  --fui-bg-color-green: rgba(9, 190, 79, 0.05);
  /* 点击背景色 */
  --fui-bg-color-hover: rgba(0, 0, 0, 0.2);
  /* 遮罩颜色 */
  --fui-bg-color-mask: rgba(0, 0, 0, 0.6);
  /* 边框颜色 */
  --fui-color-border: #eeeeee;
  /* 阴影颜色 */
  --fui-color-shadow: rgba(2, 4, 38, 0.05);
  /*禁用态的透明度 */
  --fui-opacity-disabled: 0.5;
  /* 图标尺寸 */
  --fui-img-size-sm: 48rpx;
  --fui-img-size-base: 56rpx;
  --fui-img-size-middle: 64rpx;
  --fui-img-size-lg: 96rpx;
  /* 图片尺寸 */
  --fui-img-sm: 60rpx;
  --fui-img-base: 120rpx;
  --fui-img-lg: 240rpx;
  /* Border Radius */
  --fui-border-radius-sm: 16rpx;
  --fui-border-radius-base: 24rpx;
  --fui-border-radius-lg: 48rpx;
  --fui-border-radius-circle: 50%;
  /* 水平间距 */
  --fui-spacing-row-sm: 16rpx;
  --fui-spacing-row-base: 24rpx;
  --fui-spacing-row-lg: 32rpx;
  /* 垂直间距 */
  --fui-spacing-col-sm: 8rpx;
  --fui-spacing-col-base: 16rpx;
  --fui-spacing-col-lg: 24rpx;
}

/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}
swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}
image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.height-no-tabbar {
  height: calc(100vh - var(--window-top));
}
.height-tabbar {
  height: calc(100vh - var(--window-bottom) - var(--window-top));
}
page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}
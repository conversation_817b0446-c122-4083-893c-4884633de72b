"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../store/index.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_fui_tabs2 = common_vendor.resolveComponent("fui-tabs");
  const _easycom_fui_loading2 = common_vendor.resolveComponent("fui-loading");
  const _easycom_fui_empty2 = common_vendor.resolveComponent("fui-empty");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fui_tabs2 + _easycom_fui_loading2 + _easycom_fui_empty2 + _easycom_z_paging2 + _component_layout_default_uni)();
}
const _easycom_fui_tabs = () => "../../components/firstui/fui-tabs/fui-tabs.js";
const _easycom_fui_loading = () => "../../components/firstui/fui-loading/fui-loading.js";
const _easycom_fui_empty = () => "../../components/firstui/fui-empty/fui-empty.js";
const _easycom_z_paging = () => "../../node-modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  (_easycom_fui_tabs + _easycom_fui_loading + _easycom_fui_empty + ElevatorCard + safeBottomZone + _easycom_z_paging)();
}
const safeBottomZone = () => "../../components/common/safeBottomZone.js";
const ElevatorCard = () => "./components/ElevatorCard.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    common_vendor.onLoad(() => __async(this, null, function* () {
    }));
    const userStore = store_user.useUserStore();
    const { userInfo, isLogined } = common_vendor.storeToRefs(userStore);
    const dataList = common_vendor.ref([]);
    const tabs = common_vendor.ref(["全部", "已完成", "待核对"]);
    const activeCurrent = common_vendor.ref(0);
    const changeTab = (e) => {
      var _a;
      const { index } = e;
      activeCurrent.value = index;
      console.log("index", index);
      (_a = paging.value) == null ? void 0 : _a.reload();
    };
    const handleViewElevator = (data) => {
      console.log("查看电梯详情:", data);
      common_vendor.index.showToast({
        title: `查看${data.name || "电梯"}详情`,
        icon: "none"
      });
    };
    const paging = common_vendor.ref(null);
    const queryList = (pageNo, pageSize) => __async(this, null, function* () {
      const params = {
        pageNo,
        pageSize
      };
      console.log("params", params);
      const mockData = [
        {
          id: 1,
          name: "仁恒城市星辰仁恒城市星辰仁恒城市星辰仁恒城市星辰仁恒城市星辰仁恒城市星辰仁恒城市星辰仁恒城市星辰仁恒城市星辰",
          code: "38277326262",
          buildingCount: 10,
          elevatorCount: 5,
          status: "completed"
          // 已完成
        },
        {
          id: 2,
          name: "万科城市花园",
          code: "38277326263",
          buildingCount: 8,
          elevatorCount: 12,
          status: "pending"
          // 待核对
        },
        {
          id: 3,
          name: "绿地中央广场",
          code: "38277326264",
          buildingCount: 15,
          elevatorCount: 20,
          status: "pending"
          // 待核对
        }
      ];
      paging.value.complete(mockData, true);
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(changeTab),
        b: common_vendor.p({
          tabs: common_vendor.unref(tabs),
          short: true,
          center: true
        }),
        c: common_vendor.p({
          isMask: true
        }),
        d: common_vendor.p({
          width: 386,
          height: 280,
          src: "/static/images/img_data_3x.png",
          isFixed: true,
          title: "暂无数据"
        }),
        e: common_vendor.f(common_vendor.unref(dataList), (item, k0, i0) => {
          return {
            a: common_vendor.o(handleViewElevator, item.id),
            b: "1c767bce-5-" + i0 + ",1c767bce-1",
            c: common_vendor.p({
              data: item
            }),
            d: item.id
          };
        }),
        f: common_vendor.sr(paging, "1c767bce-1,1c767bce-0", {
          "k": "paging"
        }),
        g: common_vendor.o(queryList),
        h: common_vendor.o(($event) => common_vendor.isRef(dataList) ? dataList.value = $event : null),
        i: common_vendor.p({
          ["auto-hide-loading-after-first-loaded"]: false,
          ["loading-full-fixed"]: true,
          ["paging-style"]: {
            "background-color": "#f7f7f7"
          },
          ["auto-show-back-to-top"]: true,
          auto: true,
          ["lower-threshold"]: "150rpx",
          modelValue: common_vendor.unref(dataList)
        })
      };
    };
  }
});
wx.createPage(_sfc_main);

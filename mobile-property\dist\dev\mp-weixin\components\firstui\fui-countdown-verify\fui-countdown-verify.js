"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "fui-countdown-verify",
  emits: ["send", "countdown", "end"],
  props: {
    //发送前显示文本
    text: {
      type: String,
      default: "发送验证码"
    },
    //发送中显示文本
    sending: {
      type: String,
      default: "正在发送..."
    },
    //发送后倒计时显示文本(前面会自动拼接时间)
    sent: {
      type: String,
      default: "s后重新获取"
    },
    //倒计时秒数
    seconds: {
      type: [Number, String],
      default: 60
    },
    //宽度
    width: {
      type: [Number, String],
      default: 192
    },
    //高度
    height: {
      type: [Number, String],
      default: 60
    },
    marginLeft: {
      type: [Number, String],
      default: 0
    },
    marginRight: {
      type: [Number, String],
      default: 0
    },
    //圆角
    radius: {
      type: [Number, String],
      default: 8
    },
    //字体大小 rpx
    size: {
      type: [Number, String],
      default: 24
    },
    //字体颜色
    color: {
      type: String,
      default: ""
    },
    //背景色
    background: {
      type: String,
      default: "transparent"
    },
    //边框颜色
    borderColor: {
      type: String,
      default: ""
    },
    //自定义参数
    param: {
      type: [Number, String],
      default: 0
    }
  },
  computed: {
    getColor() {
      let color = this.color;
      return color;
    },
    getBorderColor() {
      let color = this.borderColor;
      return color;
    }
  },
  data() {
    return {
      showText: "",
      //1-发送前，2-发送中 3-发送成功，倒计时
      status: 1,
      timer: null
    };
  },
  created() {
    if (this.start) {
      this.doLoop();
    } else {
      this.showText = this.text;
      this.clearTimer();
    }
  },
  beforeUnmount() {
    this.clearTimer();
  },
  methods: {
    sendCode(e) {
      if (this.status > 1)
        return;
      this.clearTimer();
      this.status = 2;
      this.showText = this.sending;
      this.$emit("send", {
        param: this.param
      });
    },
    doLoop() {
      this.clearTimer();
      this.status = 3;
      let seconds = Number(this.seconds || 60);
      this.showText = seconds + this.sent;
      this.timer = setInterval(() => {
        if (seconds > 1) {
          --seconds;
          this.showText = seconds + this.sent;
          this.$emit("countdown", {
            seconds,
            param: this.param
          });
        } else {
          this.reset();
          this.$emit("end", {
            param: this.param
          });
        }
      }, 1e3);
    },
    success() {
      this.doLoop();
    },
    reset() {
      this.clearTimer();
      this.showText = this.text;
      this.status = 1;
    },
    clearTimer() {
      clearInterval(this.timer);
      this.timer = null;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.showText),
    b: $props.size + "rpx",
    c: $props.size + "rpx",
    d: $options.getColor,
    e: !$options.getColor ? 1 : "",
    f: $data.status === 1
  }, $data.status === 1 ? {
    g: $props.radius + "rpx",
    h: common_vendor.o((...args) => $options.sendCode && $options.sendCode(...args))
  } : {}, {
    i: !$options.getBorderColor ? 1 : "",
    j: $options.getBorderColor,
    k: $props.radius * 2 + "rpx",
    l: $data.status > 1 ? 1 : "",
    m: $props.width + "rpx",
    n: $props.height + "rpx",
    o: $props.marginLeft + "rpx",
    p: $props.marginRight + "rpx",
    q: $props.radius + "rpx",
    r: $props.background,
    s: !$options.getBorderColor || $options.getBorderColor === true ? $props.background : $options.getBorderColor
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3d89bc24"]]);
wx.createComponent(Component);

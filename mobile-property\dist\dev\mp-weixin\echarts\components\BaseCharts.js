"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
if (!Math) {
  LEchart();
}
const LEchart = () => "./l-echart.js";
const _sfc_main = {
  __name: "BaseCharts",
  props: {
    options: {
      type: Object,
      default: () => ({})
    }
  },
  setup(__props) {
    const echarts = require("../static/echarts.min");
    const props = __props;
    const chartRef = common_vendor.ref(null);
    const initData = (data) => __async(this, null, function* () {
      yield common_vendor.nextTick$1();
      console.log("chartRef.value", chartRef.value);
      if (!chartRef.value)
        return;
      const myChart = yield chartRef.value.init(echarts);
      myChart.setOption(data);
    });
    common_vendor.onMounted(() => {
      setTimeout(() => {
        initData(props.options);
      }, 300);
    });
    common_vendor.watch(() => props.options, (newValue) => {
      if (Object.keys(newValue).length > 0) {
        initData(newValue);
      }
    }, {
      deep: true,
      immediate: true
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.sr(chartRef, "6313d850-0", {
          "k": "chartRef"
        })
      };
    };
  }
};
wx.createComponent(_sfc_main);
